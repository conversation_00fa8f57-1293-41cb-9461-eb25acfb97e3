{"name": "portfolio-chatbot-api", "version": "1.0.0", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "seed:enhanced": "ts-node scripts/seedEnhancedContent.ts", "seed:enhanced:clear": "ts-node scripts/seedEnhancedContent.ts --clear", "seed:integrated": "ts-node scripts/integratedSeeding.ts", "seed:pdf": "ts-node scripts/seedPDFs.ts", "db:manage": "ts-node scripts/databaseManager.ts", "db:stats": "ts-node scripts/databaseManager.ts stats", "db:test": "ts-node scripts/databaseManager.ts test", "db:optimize": "ts-node scripts/databaseManager.ts optimize", "validate": "ts-node scripts/validateSetup.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "description": "", "dependencies": {"@google/generative-ai": "^0.24.1", "@langchain/core": "^0.3.61", "@langchain/google-genai": "^0.2.14", "@pinecone-database/pinecone": "^5.1.2", "@types/express": "^5.0.3", "@types/express-session": "^1.18.2", "@types/node": "^24.0.8", "cors": "^2.8.5", "dotenv": "^17.0.0", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "express-session": "^1.18.2", "express-slow-down": "^2.1.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "langchain": "^0.3.29", "morgan": "^1.10.0", "pdf-parse": "^1.1.1", "ts-node": "^10.9.2", "typescript": "^5.8.3", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/pdf-parse": "^1.1.4", "axios": "^1.10.0"}}