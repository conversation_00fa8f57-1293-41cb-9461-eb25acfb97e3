{"cells": [{"cell_type": "markdown", "metadata": {"id": "YdsMOBaBfyT0"}, "source": ["##### Copyright 2025 Google LLC."]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "id": "rIIf_RgOf3sr"}, "outputs": [], "source": ["# @title Licensed under the Apache License, Version 2.0 (the \"License\");\n", "# you may not use this file except in compliance with the License.\n", "# You may obtain a copy of the License at\n", "#\n", "# https://www.apache.org/licenses/LICENSE-2.0\n", "#\n", "# Unless required by applicable law or agreed to in writing, software\n", "# distributed under the License is distributed on an \"AS IS\" BASIS,\n", "# WITHOUT WAR<PERSON><PERSON>IES OR CONDITIONS OF ANY KIND, either express or implied.\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License."]}, {"cell_type": "markdown", "metadata": {"id": "TySweisNf_Am"}, "source": ["# Gemini API: Question Answering using LangChain and Pinecone"]}, {"cell_type": "markdown", "metadata": {"id": "awKO767lQIWh"}, "source": ["<a target=\"_blank\" href=\"https://colab.research.google.com/github/google-gemini/cookbook/blob/main/examples/langchain/Gemini_LangChain_QA_Pinecone_WebLoad.ipynb\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" height=30/></a>"]}, {"cell_type": "markdown", "metadata": {"id": "bbf4f2c17530"}, "source": ["<!-- Princing warning Badge -->\n", "<table>\n", "  <tr>\n", "    <!-- Emoji -->\n", "    <td bgcolor=\"#f5949e\">\n", "      <font size=30>⚠️</font>\n", "    </td>\n", "    <!-- Text Content Cell -->\n", "    <td bgcolor=\"#f5949e\">\n", "      <h3><font color=black>This notebook requires paid tier rate limits to run properly.<br>  \n", "(cf. <a href=\"https://ai.google.dev/pricing#veo2\"><font color='#217bfe'>pricing</font></a> for more details).</font></h3>\n", "    </td>\n", "  </tr>\n", "</table>"]}, {"cell_type": "markdown", "metadata": {"id": "bA5Hys5PU_nt"}, "source": ["## Overview\n", "\n", "[Gemini](https://ai.google.dev/models/gemini) is a family of generative AI models that lets developers generate content and solve problems. These models are designed and trained to handle both text and images as input.\n", "\n", "[<PERSON><PERSON><PERSON><PERSON>](https://www.langchain.com/) is a data framework designed to make integration of Large Language Models (LLM) like Gemini easier for applications.\n", "\n", "[Pinecone](https://www.pinecone.io/) is a cloud-first vector database that allows users to search across billions of embeddings with ultra-low query latency.\n", "\n", "In this notebook, you'll learn how to create an application that answers questions using data from a website with the help of Gemini, LangChain, and Pinecone."]}, {"cell_type": "markdown", "metadata": {"id": "_qRjVe1tZhsx"}, "source": ["## Setup\n", "\n", "First, you must install the packages and set the necessary environment variables.\n", "\n", "### Installation\n", "\n", "Install LangChain's Python library, `langchain` and <PERSON><PERSON><PERSON><PERSON>'s integration package for Gemini, `langchain-google-genai`. Next, install Lang<PERSON>hain's integration package for the new version of Pinecone, `langchain-pinecone` and the `pinecone-client`, which is Pinecone's Python SDK. Finally, install `langchain-community` to access the `WebBaseLoader` module later."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "olK4Ejjzuj76"}, "outputs": [], "source": ["%pip install --quiet -U langchain\n", "%pip install --quiet -U langchain-google-genai\n", "%pip install --quiet -U langchain-pinecone\n", "%pip install --quiet -U pinecone\n", "%pip install --quiet -U langchain-community\n", "%pip install --quiet -U bs4"]}, {"cell_type": "markdown", "metadata": {"id": "FQOGMejVu-6D"}, "source": ["## Configure your API key\n", "\n", "To run the following cell, your API key must be stored in a Colab Secret named `GOOGLE_API_KEY`. If you don't already have an API key, or you're not sure how to create a Colab Secret, see [Authentication](https://github.com/google-gemini/cookbook/blob/main/quickstarts/Authentication.ipynb) for an example.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ysayz8skEfBW"}, "outputs": [], "source": ["import os\n", "from google.colab import userdata\n", "GOOGLE_API_KEY=userdata.get('GOOGLE_API_KEY')\n", "\n", "os.environ[\"GOOGLE_API_KEY\"] = GOOGLE_API_KEY"]}, {"cell_type": "markdown", "metadata": {"id": "MPQLjFvRooqn"}, "source": ["### Setup Pinecone\n", "\n", "To use Pinecone in your application, you must have an API key. To create an API key you have to set up a Pinecone account. Visit [Pinecone's app page](https://app.pinecone.io/), and Sign up/Log in to your account. Then navigate to the \"API Keys\" section and copy your API key.\n", "\n", "For more detailed instructions on getting the API key, you can read Pinecone's [Quickstart documentation](https://docs.pinecone.io/docs/quickstart#2-get-your-api-key).\n", "\n", "Set the environment variable `PINECONE_API_KEY` to configure Pinecone to use your API key.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "A7jTZLEApgtm"}, "outputs": [], "source": ["PINECONE_API_KEY=userdata.get('PINECONE_API_KEY')\n", "\n", "os.environ['PINECONE_API_KEY'] = PINECONE_API_KEY"]}, {"cell_type": "markdown", "metadata": {"id": "YGOKV3XflBCe"}, "source": ["## Basic steps\n", "LLMs are trained offline on a large corpus of public data. Hence they cannot answer questions based on custom or private data accurately without additional context.\n", "\n", "If you want to make use of LLMs to answer questions based on private data, you have to provide the relevant documents as context alongside your prompt. This approach is called Retrieval Augmented Generation (RAG).\n", "\n", "You will use this approach to create a question-answering assistant using the Gemini text model integrated through LangChain. The assistant is expected to answer questions about Gemini model. To make this possible you will add more context to the assistant using data from a website.\n", "\n", "In this tutorial, you'll implement the two main components in an RAG-based architecture:\n", "\n", "1. Retriever\n", "\n", "    Based on the user's query, the retriever retrieves relevant snippets that add context from the document. In this tutorial, the document is the website data.\n", "    The relevant snippets are passed as context to the next stage - \"Generator\".\n", "\n", "2. Generator\n", "\n", "    The relevant snippets from the website data are passed to the LLM along with the user's query to generate accurate answers.\n", "\n", "You'll learn more about these stages in the upcoming sections while implementing the application."]}, {"cell_type": "markdown", "metadata": {"id": "kPhs4mDkjdgY"}, "source": ["## Import the required libraries"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"id": "TcvGPVdXu05F"}, "outputs": [], "source": ["from langchain import hub\n", "from langchain import PromptTemplate\n", "from langchain.docstore.document import Document\n", "from langchain.document_loaders import WebBaseLoader\n", "from langchain.schema import StrOutputParser\n", "from langchain.schema.prompt_template import format_document\n", "from langchain.schema.runnable import RunnablePassthrough\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "from langchain_pinecone import PineconeVectorStore\n", "from pinecone import Pinecone as pc\n", "from pinecone import ServerlessSpec"]}, {"cell_type": "markdown", "metadata": {"id": "qZ3tM0T2lbVm"}, "source": ["## Retriever\n", "\n", "In this stage, you will perform the following steps:\n", "\n", "1. Read and parse the website data using LangChain.\n", "\n", "2. Create embeddings of the website data.\n", "\n", "    Embeddings are numerical representations (vectors) of text. Hence, text with similar meaning will have similar embedding vectors. You'll make use of Gemini's embedding model to create the embedding vectors of the website data.\n", "\n", "3. Store the embeddings in Pinecone's vector store.\n", "    \n", "    Pinecone is a vector database. The Pinecone vector store helps in the efficient retrieval of similar vectors. Thus, for adding context to the prompt for the LLM, relevant embeddings of the text matching the user's question can be retrieved easily using Pinecone.\n", "\n", "4. Create a Retriever from the Pinecone vector store.\n", "\n", "    The retriever will be used to pass relevant website embeddings to the LLM along with user queries."]}, {"cell_type": "markdown", "metadata": {"id": "W2N-NCPElqN3"}, "source": ["### Read and parse the website data\n", "\n", "LangChain provides a wide variety of document loaders. To read the website data as a document, you will use the `WebBaseLoader` from LangChain.\n", "\n", "To know more about how to read and parse input data from different sources using the document loaders of LangChain, read LangChain's [document loaders guide](https://python.langchain.com/docs/integrations/document_loaders)."]}, {"cell_type": "code", "execution_count": 7, "metadata": {"id": "DeNX9QFM0V-C"}, "outputs": [], "source": ["loader = WebBaseLoader(\"https://blog.google/technology/ai/google-gemini-ai/\")\n", "docs = loader.load()"]}, {"cell_type": "markdown", "metadata": {"id": "y2N6RoTDlwsM"}, "source": ["If you only want to select a specific portion of the website data to add context to the prompt, you can use regex, text slicing, or text splitting.\n", "\n", "In this example, you'll use Python's `split()` function to extract the required portion of the text. The extracted text should be converted back to <PERSON><PERSON><PERSON><PERSON>'s `Document` format."]}, {"cell_type": "code", "execution_count": 8, "metadata": {"id": "qOwDregSBVVG"}, "outputs": [], "source": ["# Extract the text from the website data document\n", "text_content = docs[0].page_content\n", "# The text content between the substrings \"code, audio, image and video.\" to\n", "# \"Cloud TPU v5p\" is relevant for this tutorial. You can use Python's `split()`\n", "# to select the required content.\n", "text_content_1 = text_content.split(\"code, audio, image and video.\",1)[1]\n", "final_text = text_content_1.split(\"Cloud TPU v5p\",1)[0]\n", "\n", "# Convert the text to <PERSON><PERSON><PERSON><PERSON>'s `Document` format\n", "docs = [Document(page_content=final_text, metadata={\"source\": \"local\"})]"]}, {"cell_type": "markdown", "metadata": {"id": "sgGVAFqWl20v"}, "source": ["### Initialize Gemini's embedding model\n", "\n", "To create the embeddings from the website data, you'll use Gemini's embedding model, **gemini-embedding-001** which supports creating text embeddings.\n", "\n", "To use this embedding model, you have to import `GoogleGenerativeAIEmbeddings` from LangChain. To know more about the embedding model, read Google AI's [language documentation](https://ai.google.dev/models/gemini)."]}, {"cell_type": "code", "execution_count": 9, "metadata": {"id": "8NXNTrjp0jdh"}, "outputs": [], "source": ["from langchain_google_genai import GoogleGenerativeAIEmbeddings\n", "\n", "gemini_embeddings = GoogleGenerativeAIEmbeddings(model=\"models/gemini-embedding-001\")"]}, {"cell_type": "markdown", "metadata": {"id": "Zr5xeWUXmnUe"}, "source": ["### Store the data using Pinecone\n", "\n", "\n", "To create a Pinecone vector database, first, you have to initialize your Pinecone client connection using the API key you set previously.\n", "\n", "In Pinecone, vector embeddings have to be stored in indexes. An index represents the vector data's top-level organizational unit. The vectors in any index must have the same dimensionality and distance metric for calculating similarity. You can read more about indexes in [Pinecone's Indexes documentation](https://docs.pinecone.io/docs/indexes).\n", "\n", "First, you'll create an index using Pinecone's `create_index` function. Pinecone allows you to create two types of indexes, Serverless indexes and Pod-based indexes. Pinecone's free starter plan lets you create only one project and one pod-based starter index with sufficient resources to support 100,000 vectors. For this tutorial, you have to create a pod-based starter index. To know more about different indexes and how they can be created, read Pinecone's [create indexes guide](https://docs.pinecone.io/docs/new-api#creating-indexes).\n", "\n", "\n", "Next, you'll insert the documents you extracted earlier from the website data into the newly created index using <PERSON><PERSON><PERSON><PERSON>'s `Pinecone.from_documents`. Under the hood, this function creates embeddings from the documents created by the document loader of <PERSON><PERSON>hain using any specified embedding model and inserts them into the specified index in a Pinecone vector database.  \n", "\n", "You have to specify the `docs` you created from the website data using <PERSON><PERSON><PERSON><PERSON>'s `WebBasedLoader` and the `gemini_embeddings` as the embedding model when invoking the `from_documents` function to create the vector database from the website data."]}, {"cell_type": "code", "execution_count": 19, "metadata": {"id": "n1VwhUQMvpcN"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating index\n", "{'deletion_protection': 'disabled',\n", " 'dimension': 3072,\n", " 'host': 'langchain-demo-u4710cy.svc.aped-4627-b74a.pinecone.io',\n", " 'metric': 'cosine',\n", " 'name': 'langchain-demo',\n", " 'spec': {'serverless': {'cloud': 'aws', 'region': 'us-east-1'}},\n", " 'status': {'ready': True, 'state': 'Ready'},\n", " 'tags': None,\n", " 'vector_type': 'dense'}\n"]}], "source": ["# Initialize Pinecone client\n", "\n", "pine_client= pc(\n", "    api_key = os.getenv(\"PINECONE_API_KEY\"),  # API key from app.pinecone.io\n", "    )\n", "index_name = \"langchain-demo\"\n", "\n", "# First, check if the index already exists. If it doesn't, create a new one.\n", "if index_name not in pine_client.list_indexes().names():\n", "    # Create a new index.\n", "    # https://docs.pinecone.io/docs/new-api#creating-a-starter-index\n", "    print(\"Creating index\")\n", "    pine_client.create_index(name=index_name,\n", "                      # `cosine` distance metric compares different documents\n", "                      # for similarity.\n", "                      # Read more about different distance metrics from\n", "                      # https://docs.pinecone.io/docs/indexes#distance-metrics.\n", "                      metric=\"cosine\",\n", "                      # The Gemini embedding model `gemini-embedding-001` uses\n", "                      # 3072 dimensions.\n", "                      dimension=3072,\n", "                      # The `pod_type` is the type of pod to use.\n", "                      # Read more about different pod types from\n", "                      # https://docs.pinecone.io/docs/pod-types.\n", "                      # Specify the pod details.\n", "                      spec=ServerlessSpec(\n", "                        cloud=\"aws\",\n", "                        region=\"us-east-1\"\n", "                        ),\n", "    )\n", "    print(pine_client.describe_index(index_name))\n", "\n", "vectorstore = PineconeVectorStore.from_documents(docs,\n", "                      gemini_embeddings, index_name=index_name)\n"]}, {"cell_type": "markdown", "metadata": {"id": "BuSjapvHnc6T"}, "source": ["### Create a retriever using Pinecone\n", "\n", "You'll now create a retriever that can retrieve website data embeddings from the newly created Pinecone vector store. This retriever can be later used to pass embeddings that provide more context to the LLM for answering user's queries.\n", "\n", "Invoke the `as_retriever` function of the vector store you initialized in the last step, to create a retriever."]}, {"cell_type": "code", "execution_count": 20, "metadata": {"id": "qndTwf0tnQDv"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n"]}], "source": ["retriever = vectorstore.as_retriever()\n", "# Check if the retriever is working by trying to fetch the relevant docs related\n", "# to the word 'MMLU'(Massive Multitask Language Understanding). If the length is\n", "# greater than zero, it means that the retriever is functioning well.\n", "print(len(retriever.invoke(\"MMLU\")))"]}, {"cell_type": "markdown", "metadata": {"id": "7Qw00lvPnjfR"}, "source": ["## Generator\n", "\n", "The Generator prompts the LLM for an answer when the user asks a question. The retriever you created in the previous stage from the Pinecone vector store will be used to pass relevant embeddings from the website data to the LLM to provide more context to the user's query.\n", "\n", "You'll perform the following steps in this stage:\n", "\n", "1. Chain together the following:\n", "    * A prompt for extracting the relevant embeddings using the retriever.\n", "    * A prompt for answering any question using Lang<PERSON>hain.\n", "    * An LLM model from Gemini for prompting.\n", "    \n", "2. Run the created chain with a question as input to prompt the model for an answer.\n"]}, {"cell_type": "markdown", "metadata": {"id": "c2MK2wLwnkLg"}, "source": ["### Initialize Gemini\n", "\n", "You must import `ChatGoogleGenerativeAI` from LangChain to initialize your model.\n", " In this example, you will use **gemini-2.0-flash**, as it supports text summarization. To know more about the text model, read Google AI's [language documentation](https://ai.google.dev/models/gemini).\n", "\n", "You can configure the model parameters such as ***temperature*** or ***top_p***,  by passing the appropriate values when initializing the `ChatGoogleGenerativeAI` LLM.  To learn more about the parameters and their uses, read Google AI's [concepts guide](https://ai.google.dev/docs/concepts#model_parameters)."]}, {"cell_type": "code", "execution_count": 21, "metadata": {"id": "CaA1vRCh7s36"}, "outputs": [], "source": ["from langchain_google_genai import ChatGoogleGenerativeAI\n", "\n", "# To configure model parameters use the `generation_config` parameter.\n", "# eg. generation_config = {\"temperature\": 0.7, \"topP\": 0.8, \"topK\": 40}\n", "# If you only want to set a custom temperature for the model use the\n", "# \"temperature\" parameter directly.\n", "\n", "llm = ChatGoogleGenerativeAI(model=\"gemini-2.5-flash\")"]}, {"cell_type": "markdown", "metadata": {"id": "2BeLN6RXnuS2"}, "source": ["### Create prompt templates\n", "\n", "You'll use <PERSON><PERSON><PERSON><PERSON>'s [PromptTemplate](https://python.langchain.com/docs/how_to/#prompt-templates) to generate prompts to the LLM for answering questions.\n", "\n", "In the `llm_prompt`, the variable `question` will be replaced later by the input question, and the variable `context` will be replaced by the relevant text from the website retrieved from the Pinecone vector store."]}, {"cell_type": "code", "execution_count": 22, "metadata": {"id": "90Czqh074dEC"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["input_variables=['context', 'question'] input_types={} partial_variables={} template=\"You are an assistant for question-answering tasks.\\nUse the following context to answer the question.\\nIf you don't know the answer, just say that you don't know.\\nUse five sentences maximum and keep the answer concise.\\n\\nQuestion: {question}\\nContext: {context}\\nAnswer:\"\n"]}], "source": ["# Prompt template to query Gemini\n", "llm_prompt_template = \"\"\"You are an assistant for question-answering tasks.\n", "Use the following context to answer the question.\n", "If you don't know the answer, just say that you don't know.\n", "Use five sentences maximum and keep the answer concise.\n", "\n", "Question: {question}\n", "Context: {context}\n", "Answer:\"\"\"\n", "\n", "llm_prompt = PromptTemplate.from_template(llm_prompt_template)\n", "\n", "print(llm_prompt)"]}, {"cell_type": "markdown", "metadata": {"id": "TkWpzMmpnx7b"}, "source": ["### Create a stuff documents chain\n", "\n", "LangChain provides [Chains](https://python.langchain.com/docs/modules/chains/) for chaining together LLMs with each other or other components for complex applications. You will create a **stuff documents chain** for this application. A stuff documents chain lets you combine all the relevant documents, insert them into the prompt, and pass that prompt to the LLM.\n", "\n", "You can create a stuff documents chain using the [LangChain Expression Language (LCEL)](https://python.langchain.com/docs/expression_language).\n", "\n", "To learn more about different types of document chains, read <PERSON><PERSON><PERSON><PERSON>'s [chains guide](https://python.langchain.com/docs/modules/chains/document/).\n", "\n", "The stuff documents chain for this application retrieves the relevant website data and passes it as the context to an LLM prompt along with the input question."]}, {"cell_type": "code", "execution_count": 23, "metadata": {"id": "gj5sWzpwp7vc"}, "outputs": [], "source": ["# Combine data from documents to readable string format.\n", "def format_docs(docs):\n", "    return \"\\n\\n\".join(doc.page_content for doc in docs)\n", "\n", "# Create stuff documents chain using LCEL.\n", "# This is called a chain because you are chaining\n", "# together different elements with the LLM.\n", "# In the following example, to create a stuff chain,\n", "# you will combine content, prompt, LLM model, and\n", "# output parser together like a chain using LCEL.\n", "#\n", "# The chain implements the following pipeline:\n", "# 1. Extract data from documents and save to the variable `context`.\n", "# 2. Use the `RunnablePassthrough` option to provide question during invoke.\n", "# 3. The `context` and `question` are then passed to the prompt and\n", "#    input variables in the prompt are populated.\n", "# 4. The prompt is then passed to the LLM (`gemini-2.0-flash`).\n", "# 5. Output from the LLM is passed through an output parser\n", "#    to structure the model response.\n", "rag_chain = (\n", "    {\"context\": retriever | format_docs, \"question\": RunnablePassthrough()}\n", "    | llm_prompt\n", "    | llm\n", "    | StrOutputParser()\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "gmHx_F7DoMgM"}, "source": ["### Prompt the model\n", "\n", "You can now query the LLM by passing any question to the `invoke()` function of the stuff documents chain you created previously."]}, {"cell_type": "code", "execution_count": 26, "metadata": {"id": "95W-sbTjoGGj"}, "outputs": [{"data": {"text/markdown": ["Gemini is a state-of-the-art, natively multimodal AI model developed by Google. It is designed to understand and reason about various inputs, including text, images, audio, and video, from the ground up. Optimized for different sizes like Ultra, Pro, and Nano, Gemini can efficiently run on a wide range of devices, from data centers to mobile phones. It demonstrates sophisticated reasoning capabilities, excelling at complex tasks, extracting insights, and generating high-quality code. Gemini has surpassed current state-of-the-art performance on numerous benchmarks, including outperforming human experts on MMLU."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["from IPython.display import Markdown\n", "\n", "Markdown(rag_chain.invoke(\"What is <PERSON>?\"))"]}, {"cell_type": "markdown", "metadata": {"id": "82c542e5cc5f"}, "source": ["## Summary\n", "\n", "Gemini API works great with Langchain. The integration is seamless and provides an easy interface for:\n", "- loading and splitting files\n", "- creating Pinecone database with embeddings\n", "- answering questions based on context from files"]}, {"cell_type": "markdown", "metadata": {"id": "d2e923a14533"}, "source": ["## What's next?\n", "\n", "This notebook showed only one possible use case for langchain with Gemini API. You can find many more [here](../../examples/langchain)."]}], "metadata": {"colab": {"name": "<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>A_Pinecone_WebLoad.ipynb", "toc_visible": true}, "kernelspec": {"display_name": "Python 3", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 0}