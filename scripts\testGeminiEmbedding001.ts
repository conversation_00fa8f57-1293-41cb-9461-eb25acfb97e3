import dotenv from 'dotenv';
import { GoogleGenerativeAIEmbeddings } from '@langchain/google-genai';

// Load environment variables
dotenv.config();

/**
 * Test script to verify gemini-embedding-001 model works correctly
 */
async function testGeminiEmbedding001() {
  try {
    console.log('🧪 Testing Gemini Embedding 001 Model...');
    
    if (!process.env.GOOGLE_AI_API_KEY) {
      throw new Error('GOOGLE_AI_API_KEY is required');
    }

    // Initialize the new embedding model
    const embeddings = new GoogleGenerativeAIEmbeddings({
      apiKey: process.env.GOOGLE_AI_API_KEY!,
      model: "models/gemini-embedding-001",
    });

    console.log('✅ Embedding model initialized successfully');

    // Test 1: Basic embedding generation
    console.log('\n📝 Test 1: Basic embedding generation');
    const testText = "Hello, I am a portfolio chatbot built with React and Node.js";
    
    console.log(`Input text: "${testText}"`);
    const embedding = await embeddings.embedQuery(testText);
    
    console.log(`✅ Generated embedding with ${embedding.length} dimensions`);
    console.log(`📊 Expected: 3072 dimensions, Got: ${embedding.length} dimensions`);
    
    if (embedding.length === 3072) {
      console.log('✅ Dimension check passed!');
    } else {
      console.log('❌ Dimension check failed!');
      return;
    }

    // Test 2: Multiple embeddings
    console.log('\n📝 Test 2: Multiple embeddings');
    const testTexts = [
      "I am a web developer specializing in React and TypeScript",
      "I enjoy building full-stack applications with modern technologies",
      "My experience includes mobile development with Flutter and Firebase"
    ];

    const embeddings_batch = await embeddings.embedDocuments(testTexts);
    console.log(`✅ Generated ${embeddings_batch.length} embeddings`);
    
    for (let i = 0; i < embeddings_batch.length; i++) {
      console.log(`   Embedding ${i + 1}: ${embeddings_batch[i].length} dimensions`);
    }

    // Test 3: Similarity calculation
    console.log('\n📝 Test 3: Similarity calculation');
    const text1 = "I love programming and building web applications";
    const text2 = "I enjoy coding and creating websites";
    const text3 = "I like eating pizza and watching movies";

    const emb1 = await embeddings.embedQuery(text1);
    const emb2 = await embeddings.embedQuery(text2);
    const emb3 = await embeddings.embedQuery(text3);

    // Calculate cosine similarity
    const similarity12 = cosineSimilarity(emb1, emb2);
    const similarity13 = cosineSimilarity(emb1, emb3);

    console.log(`Similarity between programming texts: ${similarity12.toFixed(4)}`);
    console.log(`Similarity between programming and food: ${similarity13.toFixed(4)}`);
    
    if (similarity12 > similarity13) {
      console.log('✅ Semantic similarity test passed! (Programming texts are more similar)');
    } else {
      console.log('⚠️ Semantic similarity test unexpected result');
    }

    // Test 4: Technical content embedding
    console.log('\n📝 Test 4: Technical content embedding');
    const technicalText = `
      This portfolio chatbot is built using a RAG (Retrieval Augmented Generation) architecture.
      It uses LangChain for orchestration, Pinecone for vector storage, and Gemini for both
      embeddings and text generation. The backend is built with Node.js and Express, while
      the frontend uses React and TypeScript.
    `;

    const techEmbedding = await embeddings.embedQuery(technicalText.trim());
    console.log(`✅ Technical content embedded: ${techEmbedding.length} dimensions`);

    // Test 5: Performance timing
    console.log('\n📝 Test 5: Performance timing');
    const startTime = Date.now();
    
    await embeddings.embedQuery("Performance test query for timing measurement");
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`✅ Embedding generation took ${duration}ms`);
    
    if (duration < 5000) {
      console.log('✅ Performance test passed! (Under 5 seconds)');
    } else {
      console.log('⚠️ Performance test slow (Over 5 seconds)');
    }

    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`✅ Model: models/gemini-embedding-001`);
    console.log(`✅ Dimensions: ${embedding.length}`);
    console.log(`✅ Semantic understanding: Working`);
    console.log(`✅ Performance: ${duration}ms per embedding`);
    console.log('\n🚀 Ready to proceed with migration!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    
    if (error.message?.includes('API key')) {
      console.log('\n💡 Troubleshooting:');
      console.log('1. Check your GOOGLE_AI_API_KEY in .env file');
      console.log('2. Ensure the API key is valid and has proper permissions');
      console.log('3. Check your Google AI Studio quota');
    } else if (error.message?.includes('model')) {
      console.log('\n💡 Troubleshooting:');
      console.log('1. Ensure you have access to gemini-embedding-001 model');
      console.log('2. Check if the model name is correct: "models/gemini-embedding-001"');
      console.log('3. Verify your API key has access to the latest models');
    } else {
      console.log('\n💡 Troubleshooting:');
      console.log('1. Check your internet connection');
      console.log('2. Verify all environment variables are set');
      console.log('3. Check Google AI service status');
    }
    
    process.exit(1);
  }
}

/**
 * Calculate cosine similarity between two vectors
 */
function cosineSimilarity(vecA: number[], vecB: number[]): number {
  if (vecA.length !== vecB.length) {
    throw new Error('Vectors must have the same length');
  }

  let dotProduct = 0;
  let normA = 0;
  let normB = 0;

  for (let i = 0; i < vecA.length; i++) {
    dotProduct += vecA[i] * vecB[i];
    normA += vecA[i] * vecA[i];
    normB += vecB[i] * vecB[i];
  }

  normA = Math.sqrt(normA);
  normB = Math.sqrt(normB);

  if (normA === 0 || normB === 0) {
    return 0;
  }

  return dotProduct / (normA * normB);
}

// Command line interface
if (require.main === module) {
  testGeminiEmbedding001().catch(console.error);
}

export { testGeminiEmbedding001 };
