# Google AI API Key for Gemini LLM
GOOGLE_AI_API_KEY=your_gemini_api_key_here

# Pinecone Vector Database Configuration
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_INDEX=your_pinecone_index_name_here

# Server Configuration
PORT=3000
NODE_ENV=development

# Frontend URL for CORS (comma-separated for multiple origins)
FRONTEND_URL=http://localhost:3000
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,https://localhost:3000,https://localhost:3001

# Security Configuration (Smart rate limiting - shared IP friendly)
MAX_MESSAGE_LENGTH=1000
MAX_REQUESTS_PER_MINUTE=50
MAX_REQUESTS_PER_HOUR=500
SLOW_DOWN_THRESHOLD=20

# Optional: Security Feature Controls
ENABLE_IP_BLOCKING=true
ENABLE_SUSPICIOUS_PATTERN_DETECTION=true
LOG_LEVEL=info

# Session Configuration (Required for chat session tracking)
SESSION_SECRET=your-secure-session-secret-change-in-production

# For production (Render), use:
# FRONTEND_URL=https://your-portfolio-domain.github.io
# ALLOWED_ORIGINS=https://your-portfolio-domain.github.io,https://your-custom-domain.com